@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --primary: #6c63ff;
    --primary-dark: #4d44d9;
    --secondary: #00d9ff;
    --accent: #ff00cc;
    --dark: #121212;
    --dark-lighter: #1e1e1e;
    --light: #ffffff;
    --light-gray: #f5f5f5;
    --gray: #b3b3b3;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --gradient-1: linear-gradient(45deg, var(--primary) 0%, var(--secondary) 100%);
    --gradient-2: linear-gradient(45deg, var(--accent) 0%, var(--primary) 100%);
    --gradient-3: linear-gradient(45deg, var(--secondary) 0%, var(--accent) 100%);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;

    /* Glow effects */
    --glow-primary: 0 0 20px rgba(108, 99, 255, 0.6), 0 0 40px rgba(108, 99, 255, 0.4), 0 0 60px rgba(108, 99, 255, 0.2);
    --glow-secondary: 0 0 20px rgba(0, 217, 255, 0.6), 0 0 40px rgba(0, 217, 255, 0.4), 0 0 60px rgba(0, 217, 255, 0.2);
    --glow-accent: 0 0 20px rgba(255, 0, 204, 0.6), 0 0 40px rgba(255, 0, 204, 0.4), 0 0 60px rgba(255, 0, 204, 0.2);

    /* Enhanced Easing Functions */
    --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
    --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
    --ease-in-out-quint: cubic-bezier(0.83, 0, 0.17, 1);
    --ease-out-back: cubic-bezier(0.34, 1.56, 0.64, 1);
    --ease-in-out-circ: cubic-bezier(0.85, 0, 0.15, 1);
    --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Transitions */
    --transition-fast: 0.2s var(--ease-out-quart);
    --transition-normal: 0.3s var(--ease-out-expo);
    --transition-slow: 0.5s var(--ease-in-out-quint);
    --transition-spring: 0.6s var(--ease-spring);
    --transition-back: 0.4s var(--ease-out-back);

    /* Animation Durations */
    --duration-fast: 0.2s;
    --duration-normal: 0.4s;
    --duration-slow: 0.8s;
    --duration-slower: 1.2s;

    /* Blur Effects */
    --blur-sm: 4px;
    --blur-md: 8px;
    --blur-lg: 16px;
    --blur-xl: 24px;

    /* Z-index layers */
    --z-background: -1;
    --z-content: 1;
    --z-header: 100;
    --z-modal: 200;
    --z-tooltip: 300;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

/* Custom Selection Colors */
::selection {
    background: rgba(108, 99, 255, 0.3);
    color: var(--text-primary);
    text-shadow: none;
}

::-moz-selection {
    background: rgba(108, 99, 255, 0.3);
    color: var(--text-primary);
    text-shadow: none;
}

/* Custom Focus Indicators */
*:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    border-radius: var(--border-radius-sm);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--dark);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: var(--z-tooltip);
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* Focus styles for better accessibility */
*:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

button:focus,
a:focus {
    outline: 2px solid var(--secondary);
    outline-offset: 2px;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--dark);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 1;
    transition: opacity var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
}

.loading-logo {
    margin-bottom: 2rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(108, 99, 255, 0.3);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Theme Toggle */
.theme-toggle {
    position: fixed;
    top: 50%;
    right: 2rem;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    cursor: pointer;
    z-index: var(--z-header);
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: rgba(30, 30, 30, 0.9);
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--glow-primary);
}

.theme-toggle .fas {
    font-size: 1.2rem;
    transition: opacity var(--transition-fast);
}

.theme-toggle .fa-sun {
    opacity: 0;
    position: absolute;
}

body.light-theme .theme-toggle .fa-moon {
    opacity: 0;
}

body.light-theme .theme-toggle .fa-sun {
    opacity: 1;
}

/* Animated Background */
.animated-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: -1;
    overflow: hidden;
}

.gradient-blob {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    opacity: 0.15;
    animation: float 20s ease-in-out infinite;
}

.blob-1 {
    background: var(--primary);
    width: 50vw;
    height: 50vw;
    top: -25vw;
    left: -10vw;
    animation-delay: 0s;
}

.blob-2 {
    background: var(--secondary);
    width: 40vw;
    height: 40vw;
    bottom: -20vw;
    right: -10vw;
    animation-delay: -5s;
}

.blob-3 {
    background: var(--accent);
    width: 60vw;
    height: 60vw;
    top: 30vh;
    left: 50vw;
    animation-delay: -10s;
}

@keyframes float {
    0% {
        transform: translate(0, 0) scale(1);
    }
    33% {
        transform: translate(5%, 5%) scale(1.05);
    }
    66% {
        transform: translate(-5%, 2%) scale(0.95);
    }
    100% {
        transform: translate(0, 0) scale(1);
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
}

h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

p {
    margin-bottom: 1rem;
}

.gradient-text {
    background: var(--gradient-1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline;
}

/* Navigation */
nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 1.5rem 0;
    z-index: 100;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    background-color: rgba(18, 18, 18, 0.8);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--secondary);
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background: var(--gradient-1);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

.logo a {
    text-decoration: none;
    color: inherit;
}

/* Enhanced Multi-Element Button */
.launch-btn {
    background: var(--gradient-1);
    color: white !important;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    box-shadow: var(--glow-primary);
    transition: all var(--transition-normal);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.launch-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 0 40px rgba(108, 99, 255, 0.9), 0 0 80px rgba(108, 99, 255, 0.7), 0 0 120px rgba(108, 99, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
}

.launch-btn::after {
    display: none;
}

/* Shimmer Effect */
.launch-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-slow);
}

.launch-btn:hover::before {
    left: 100%;
}

/* Button Icon Animation */
.launch-btn i {
    transition: transform var(--transition-spring);
}

.launch-btn:hover i {
    transform: translateX(3px) rotate(15deg);
}

/* Ripple Effect */
.launch-btn .ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple var(--duration-normal) var(--ease-out-expo);
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.mobile-menu-icon {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    padding: 10rem 2rem 6rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-title-line {
    display: block;
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.8s ease forwards;
}

.hero-title-line:nth-child(1) {
    animation-delay: 0.2s;
}

.hero-title-line:nth-child(2) {
    animation-delay: 0.4s;
}

.hero-title-line:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
    padding: 1.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    max-width: 530px;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

/* Enhanced Primary Button */
.primary-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: var(--gradient-1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    box-shadow: var(--glow-primary);
    position: relative;
    overflow: hidden;
}

.primary-button:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 0 40px rgba(108, 99, 255, 0.9), 0 0 80px rgba(108, 99, 255, 0.7), 0 0 120px rgba(108, 99, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
}

.primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.primary-button:hover::before {
    left: 100%;
}

/* Enhanced Secondary Button */
.secondary-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.secondary-button:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--glow-secondary);
    border-color: rgba(0, 217, 255, 0.5);
}

.secondary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 217, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.secondary-button:hover::before {
    left: 100%;
}

/* Button Icon Animations */
.primary-button i,
.secondary-button i {
    transition: transform var(--transition-spring);
}

.primary-button:hover i,
.secondary-button:hover i {
    transform: translateX(2px);
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    max-width: 550px;
}

.demo-visualiser {
    width: 100%;
    height: 300px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#heroCanvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* Demo Overlay */
.demo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.3));
    opacity: 0;
    transition: opacity var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.demo-visualiser:hover .demo-overlay {
    opacity: 1;
}

.demo-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.demo-control-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    color: var(--dark);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.demo-control-btn:hover {
    transform: scale(1.1);
    background: white;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

.demo-info {
    background: rgba(0, 0, 0, 0.8);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

/* Features Section */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.features {
    padding: 6rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: rgba(30, 30, 30, 0.5);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    background: var(--gradient-1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1;
}

.feature-card h3 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.feature-card p {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
}

/* Feature Benefits */
.feature-benefits {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.benefit-tag {
    background: rgba(108, 99, 255, 0.1);
    color: var(--primary);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(108, 99, 255, 0.2);
    transition: all var(--transition-normal);
}

.feature-card:hover .benefit-tag {
    background: rgba(108, 99, 255, 0.2);
    border-color: rgba(108, 99, 255, 0.4);
    transform: translateY(-2px);
}



/* Demos Section */
.demos {
    padding: 6rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.demo-item {
    background: rgba(30, 30, 30, 0.5);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.demo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
}

.demo-preview {
    width: 100%;
    height: 200px;
    position: relative;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.3);
}

.demo-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

.demo-info {
    padding: 1.5rem;
}

.demo-info h3 {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
}

.demo-info p {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
}

.demo-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.demo-link:hover {
    color: var(--secondary);
    text-shadow: var(--glow-secondary);
}

/* How It Works Section */
.how-it-works {
    padding: 6rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    margin-top: 4rem;
    position: relative;
}

.steps-container::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 2rem;
    width: 2px;
    background: linear-gradient(to bottom, transparent, var(--primary), var(--secondary), transparent);
    transform: translateX(-50%);
}

.step {
    display: flex;
    gap: 2rem;
    position: relative;
}

.step-number {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background: var(--gradient-1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    flex-shrink: 0;
    z-index: 1;
}

.step-content {
    background: rgba(30, 30, 30, 0.5);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.05);
    flex-grow: 1;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transform: translateX(0);
    transition: transform 0.5s ease;
}

.step-content:hover {
    transform: translateX(10px);
}

/* FAQ Section */
.faq {
    padding: 6rem 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.faq-container {
    margin-top: 3rem;
}

.faq-item {
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.faq-item:hover {
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.faq-question {
    width: 100%;
    padding: 1.5rem 2rem;
    background: rgba(30, 30, 30, 0.3);
    border: none;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    text-align: left;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all var(--transition-normal);
}

.faq-question:hover {
    background: rgba(30, 30, 30, 0.5);
}

.faq-question[aria-expanded="true"] {
    background: rgba(108, 99, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.faq-question i {
    transition: transform var(--transition-normal);
    color: var(--primary);
}

.faq-question[aria-expanded="true"] i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 2rem;
    background: rgba(0, 0, 0, 0.2);
    max-height: 0;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.faq-answer[aria-hidden="false"] {
    padding: 1.5rem 2rem;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* About Section */
.about {
    padding: 6rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.about-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.about-content {
    max-width: 800px;
    text-align: center;
}

.about-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.about-cta {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.about-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    justify-content: center;
}

.about-stats .stat {
    background: rgba(30, 30, 30, 0.5);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all var(--transition-normal);
}

.about-stats .stat:hover {
    background: rgba(30, 30, 30, 0.7);
    border-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Footer */
footer {
    background-color: rgba(18, 18, 18, 0.8);
    padding: 4rem 0 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-logo {
    flex-grow: 1;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--secondary);
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    transition: all 0.3s ease;
    text-decoration: none;
}

.footer-social a:hover {
    background: var(--gradient-1);
    transform: translateY(-3px);
}

.footer-bottom {
    max-width: 1200px;
    margin: 2rem auto 0;
    padding: 2rem 2rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Mobile Navigation */
.mobile-nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    max-width: 400px;
    height: 100vh;
    background-color: var(--dark-lighter);
    z-index: var(--z-modal);
    transition: all var(--transition-slow);
    backdrop-filter: blur(10px);
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav.active {
    right: 0;
}

.mobile-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--z-modal) - 1);
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-slow);
}

.mobile-nav-overlay.active {
    opacity: 1;
    pointer-events: all;
}

.mobile-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav-title {
    font-size: 1.25rem;
    font-weight: 600;
    background: var(--gradient-1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.mobile-nav-close {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all var(--transition-normal);
}

.mobile-nav-close:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.mobile-nav-links {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 0;
}

.mobile-nav-links a {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: all var(--transition-normal);
    position: relative;
}

.mobile-nav-links a:hover {
    color: var(--secondary);
    padding-left: 1rem;
}

.mobile-nav-links a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 2px;
    background: var(--gradient-1);
    transition: width var(--transition-normal);
}

.mobile-nav-links a:hover::before {
    width: 20px;
}

.mobile-launch-btn {
    background: var(--gradient-1);
    color: white !important;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    margin-top: 2rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    text-decoration: none;
    box-shadow: var(--glow-primary);
    border: none;
}

.mobile-launch-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(108, 99, 255, 0.8), 0 0 60px rgba(108, 99, 255, 0.6), 0 0 90px rgba(108, 99, 255, 0.4);
    padding-left: 1.5rem !important;
}

.mobile-launch-btn::before {
    display: none;
}

/* Glow Utility Classes */
.glow-primary {
    box-shadow: var(--glow-primary);
}

.glow-secondary {
    box-shadow: var(--glow-secondary);
}

.glow-accent {
    box-shadow: var(--glow-accent);
}

.glow-pulse {
    animation: glowPulse 2s ease-in-out infinite alternate;
}

@keyframes glowPulse {
    from {
        box-shadow: var(--glow-primary);
    }
    to {
        box-shadow: 0 0 30px rgba(108, 99, 255, 0.8), 0 0 60px rgba(108, 99, 255, 0.6), 0 0 90px rgba(108, 99, 255, 0.4);
    }
}

/* Enhanced Animation Classes */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes blurToFocus {
    from {
        opacity: 0;
        filter: blur(var(--blur-lg));
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        filter: blur(0);
        transform: translateY(0) scale(1);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
        filter: blur(var(--blur-sm));
    }
    to {
        opacity: 1;
        transform: translateX(0);
        filter: blur(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
        filter: blur(var(--blur-sm));
    }
    to {
        opacity: 1;
        transform: translateX(0);
        filter: blur(0);
    }
}

@keyframes scaleInBlur {
    from {
        opacity: 0;
        transform: scale(0.8);
        filter: blur(var(--blur-md));
    }
    to {
        opacity: 1;
        transform: scale(1);
        filter: blur(0);
    }
}

@keyframes borderGlow {
    0% {
        border-color: transparent;
        box-shadow: 0 0 0 0 rgba(108, 99, 255, 0);
    }
    50% {
        border-color: rgba(108, 99, 255, 0.5);
        box-shadow: 0 0 20px 0 rgba(108, 99, 255, 0.3);
    }
    100% {
        border-color: var(--primary);
        box-shadow: 0 0 30px 0 rgba(108, 99, 255, 0.5);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn var(--duration-slow) var(--ease-out-expo) forwards;
}

.slide-in {
    animation: slideIn var(--duration-slow) var(--ease-out-expo) forwards;
}

.blur-to-focus {
    animation: blurToFocus var(--duration-slower) var(--ease-out-expo) forwards;
}

.slide-in-left {
    animation: slideInLeft var(--duration-slow) var(--ease-out-expo) forwards;
}

.slide-in-right {
    animation: slideInRight var(--duration-slow) var(--ease-out-expo) forwards;
}

.scale-in-blur {
    animation: scaleInBlur var(--duration-slow) var(--ease-out-back) forwards;
}

.border-glow {
    animation: borderGlow var(--duration-normal) var(--ease-out-expo) forwards;
}

/* Staggered Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Glass Morphism Effects */
.glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-strong {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Mouse Interaction Effects */
.magnetic {
    transition: transform var(--transition-normal);
}

.magnetic:hover {
    transform: scale(1.05);
}

.parallax-element {
    transition: transform var(--transition-fast);
    will-change: transform;
}

/* 3D Transform Effects */
.card-3d {
    transform-style: preserve-3d;
    transition: transform var(--transition-normal);
}

.card-3d:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
}

/* Border Animation Effects */
.animated-border {
    position: relative;
    border: 1px solid transparent;
    background: linear-gradient(var(--dark), var(--dark)) padding-box,
                var(--gradient-1) border-box;
    transition: all var(--transition-normal);
}

.animated-border:hover {
    background: linear-gradient(var(--dark), var(--dark)) padding-box,
                var(--gradient-2) border-box;
    box-shadow: 0 0 20px rgba(108, 99, 255, 0.3);
}

.border-glow-hover {
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.border-glow-hover:hover {
    border-color: var(--primary);
    box-shadow: 0 0 20px rgba(108, 99, 255, 0.4);
}

/* Enhanced Glow Effects */
.glow-on-hover {
    transition: all var(--transition-normal);
}

.glow-on-hover:hover {
    box-shadow: 0 0 30px rgba(108, 99, 255, 0.6),
                0 0 60px rgba(108, 99, 255, 0.4),
                0 0 90px rgba(108, 99, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding-top: 8rem;
    }
    
    .hero-content {
        max-width: 100%;
        margin-bottom: 4rem;
    }
    
    .hero-description {
        margin-left: auto;
        margin-right: auto;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .steps-container::before {
        left: 1.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .theme-toggle {
        right: 1rem;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .nav-links {
        display: none;
    }
    
    .mobile-menu-icon {
        display: block;
    }
    
    .steps-container::before {
        display: none;
    }
    
    .step {
        flex-direction: column;
    }
    
    .step-number {
        margin: 0 auto;
        margin-bottom: -2rem;
        z-index: 2;
    }
    
    .step-content {
        padding-top: 3rem;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem 0;
    }

    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .theme-toggle {
        right: 0.5rem;
        width: 40px;
        height: 40px;
    }

    .mobile-nav {
        width: 90%;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .primary-button, 
    .secondary-button {
        width: 100%;
        justify-content: center;
    }
    
    .section-title {
        font-size: 2rem;
    }
}
